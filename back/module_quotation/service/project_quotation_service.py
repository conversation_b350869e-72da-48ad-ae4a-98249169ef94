"""
项目报价服务
"""

import time
import io
import os
from datetime import datetime
from decimal import Decimal
from typing import Optional
from urllib.parse import quote

from sqlalchemy import and_, select, func, delete
from sqlalchemy.ext.asyncio import AsyncSession
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, Border, Side
from fastapi.responses import StreamingResponse
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
from reportlab.lib.units import inch
from utils.excel_template_engine import ExcelTemplateEngine

from config.base_service import BaseService
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_quotation.entity.do.project_quotation_attachment_do import ProjectQuotationAttachment
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
from module_quotation.entity.do.project_quotation_other_fee_do import ProjectQuotationOtherFee
from module_quotation.entity.do.project_quotation_total_fee_do import ProjectQuotationTotalFee
from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
from module_quotation.entity.vo.project_quotation_vo import (
    AddProjectQuotationModel,
    EditProjectQuotationModel,
    ProjectQuotationPageQueryModel,
    ProjectQuotationQueryModel,
    ProjectQuotationTotalFeeModel,
    ProjectQuotationFeeCalculationModel,
)
from module_quotation.service.project_quotation_fee_calculation_service import ProjectQuotationFeeCalculationService
from module_quotation.service.project_quotation_customer_support_service import ProjectQuotationCustomerSupportService
from module_quotation.entity.do.project_quotation_customer_support_do import ProjectQuotationCustomerSupport
from utils.common_util import CamelCaseUtil
from utils.export_util import ExportUtil


class ProjectQuotationService(BaseService[ProjectQuotation]):
    """
    项目报价服务
    """

    def __init__(self, db: AsyncSession):
        """
        初始化

        :param db: 数据库会话
        """
        super().__init__(ProjectQuotation, db)
        self.db = db

    @classmethod
    def _calculate_total_tee(
        cls,
        project_quotation_id: int,
        item_testing_fees: Decimal,
        other_fees: Decimal,
        total_fee: Optional[ProjectQuotationTotalFeeModel] = None,
    ) -> ProjectQuotationTotalFee:
        """
        计算项目报价的各项总费用
        """
        if not total_fee:
            total_fee = ProjectQuotationTotalFeeModel.model_validate(
                dict(
                    discount_rate=100,
                    tax_rate=0,
                    adjustment_amount=0,
                )
            )
        # 检测折后费用
        discount_rate = total_fee.discount_rate or Decimal("100")
        discounted_testing_fee = item_testing_fees * discount_rate / 100
        # 优惠前总费用 = 检测折后费用 + 其他费用
        total_fee_before_discount = discounted_testing_fee + other_fees
        # 税费
        tax_rate = total_fee.tax_rate or Decimal("0")
        tax = total_fee_before_discount * tax_rate / 100
        # 优惠前总费用(税后) =  总费用 + 税费
        total_fee_after_tax = total_fee_before_discount + tax
        # 调整金额
        adjustment_amount = total_fee.adjustment_amount or Decimal("0")
        # 最终金额
        final_amount = total_fee_after_tax + adjustment_amount

        project_quotation_total_fee = ProjectQuotationTotalFee(
            project_quotation_id=project_quotation_id,
            item_testing_fees=item_testing_fees,
            discount_rate=discount_rate,
            discounted_testing_fee=discounted_testing_fee,
            other_fee=other_fees,
            total_fee_before_discount=total_fee_before_discount,
            tax_rate=tax_rate,
            tax=tax,
            total_fee_after_tax=total_fee_after_tax,
            adjustment_amount=adjustment_amount,
            final_amount=final_amount,
            create_time=datetime.now(),
            update_time=datetime.now(),
        )
        return project_quotation_total_fee

    async def _get_project_quotation_page_base(
        self, query_object: ProjectQuotationPageQueryModel, is_approved_only: bool = False, current_user: CurrentUserModel = None
    ):
        """
        分页查询项目报价的基础方法

        :param query_object: 分页查询参数对象
        :param is_approved_only: 是否只查询已审批的项目
        :return: 分页查询结果
        """
        # 构建查询条件
        conditions = []

        if is_approved_only:
            conditions.append(ProjectQuotation.status == "2")

        if query_object.project_name:
            conditions.append(ProjectQuotation.project_name.like(f"%{query_object.project_name}%"))
        if query_object.project_code:
            conditions.append(ProjectQuotation.project_code.like(f"%{query_object.project_code}%"))
        if query_object.customer_name:
            conditions.append(ProjectQuotation.customer_name.like(f"%{query_object.customer_name}%"))
        if query_object.status and not is_approved_only:
            conditions.append(ProjectQuotation.status == query_object.status)
        if query_object.begin_time and query_object.end_time:
            conditions.append(ProjectQuotation.create_time.between(query_object.begin_time, query_object.end_time))
        elif query_object.begin_time:
            conditions.append(ProjectQuotation.create_time >= query_object.begin_time)
        elif query_object.end_time:
            conditions.append(ProjectQuotation.create_time <= query_object.end_time)

        # 执行查询
        stmt = select(ProjectQuotation).where(and_(*conditions)).order_by(ProjectQuotation.create_time.desc())

        # 计算总数
        count_stmt = select(func.count()).select_from(ProjectQuotation).where(and_(*conditions))
        total = await self.db.execute(count_stmt)
        total = total.scalar() or 0

        # 分页
        stmt = stmt.offset((query_object.page_num - 1) * query_object.page_size).limit(query_object.page_size)
        result = await self.db.execute(stmt)
        quotations = result.scalars().all()

        # 转换为VO对象
        quotation_list = []
        for quotation in quotations:
            quotation_dict = await self.get_project_quotation(quotation.id)
            
            # 添加权限字段
            if current_user:
                quotation_dict["can_submit_approval"] = await self._can_user_submit_approval(quotation.id, current_user)
                quotation_dict["can_withdraw_approval"] = await self._can_user_withdraw_approval(quotation.id, current_user)
            
            quotation_list.append(quotation_dict)

        return {"total": total, "rows": quotation_list}

    async def get_project_quotation_page(self, query_object: ProjectQuotationPageQueryModel, current_user: CurrentUserModel = None):
        """
        分页查询项目报价

        :param query_object: 分页查询参数对象
        :param current_user: 当前用户
        :return: 分页查询结果
        """
        result = await self._get_project_quotation_page_base(query_object, current_user=current_user)
        return CamelCaseUtil.transform_result(result)

    async def get_approved_project_quotation_list(self, query_object: ProjectQuotationPageQueryModel):
        """
        查询已审批项目报价列表，用于采样任务分配页面

        :param query_object: 分页查询参数对象
        :return: 已审批项目报价分页查询结果
        """
        result = await self._get_project_quotation_page_base(query_object, is_approved_only=True)
        return CamelCaseUtil.transform_result(result)

    async def get_project_quotation_list(self, query_object: ProjectQuotationQueryModel):
        """
        查询项目报价列表

        :param query_object: 查询参数对象
        :return: 项目报价列表
        """
        # 构建查询条件
        conditions = []

        if query_object.project_name:
            conditions.append(ProjectQuotation.project_name.like(f"%{query_object.project_name}%"))
        if query_object.project_code:
            conditions.append(ProjectQuotation.project_code.like(f"%{query_object.project_code}%"))
        if query_object.customer_name:
            conditions.append(ProjectQuotation.customer_name.like(f"%{query_object.customer_name}%"))
        if query_object.status:
            conditions.append(ProjectQuotation.status == query_object.status)
        if query_object.begin_time and query_object.end_time:
            conditions.append(ProjectQuotation.create_time.between(query_object.begin_time, query_object.end_time))
        elif query_object.begin_time:
            conditions.append(ProjectQuotation.create_time >= query_object.begin_time)
        elif query_object.end_time:
            conditions.append(ProjectQuotation.create_time <= query_object.end_time)

        # 执行查询
        stmt = select(ProjectQuotation).where(and_(*conditions)).order_by(ProjectQuotation.create_time.desc())
        result = await self.db.execute(stmt)
        quotations = result.scalars().all()

        # 转换为VO对象
        quotation_list = []
        for quotation in quotations:
            # 查询明细项
            items_stmt = select(ProjectQuotationItem).where(
                ProjectQuotationItem.project_quotation_id == quotation.id,
            )
            items_result = await self.db.execute(items_stmt)
            items = items_result.scalars().all()

            # 查询附件
            attachments_stmt = select(ProjectQuotationAttachment).where(
                ProjectQuotationAttachment.project_quotation_id == quotation.id,
            )
            attachments_result = await self.db.execute(attachments_stmt)
            attachments = attachments_result.scalars().all()

            # 构建基本信息字典

            # 使用CamelCaseUtil转换为驼峰命名
            base_dict = {
                "id": quotation.id,
                "project_name": quotation.project_name,
                "project_code": quotation.project_code,
                "contract_code": quotation.contract_code,
                "customer_id": quotation.customer_id,
                "customer_name": quotation.customer_name,
                "customer_address": quotation.customer_address,
                "customer_contact": quotation.customer_contact,
                "customer_phone": quotation.customer_phone,
                "inspected_party": quotation.inspected_party,
                "inspected_contact": quotation.inspected_contact,
                "inspected_phone": quotation.inspected_phone,
                "inspected_address": quotation.inspected_address,
                "project_manager": quotation.project_manager,
                "market_manager": quotation.market_manager,
                "technical_manager": quotation.technical_manager,
                "commission_date": quotation.commission_date,
                # approvers字段已废弃，使用approver_list
                "status": quotation.status,
                "remark": quotation.remark,
                "create_by": quotation.create_by,
                "create_time": quotation.create_time,
                "update_by": quotation.update_by,
                "update_time": quotation.update_time,
                "items": [
                    CamelCaseUtil.transform_result(
                        {
                            "id": item.id,
                            "item_code": item.item_code,
                            "category": item.category,
                            "parameter": item.parameter,
                            "method": item.method,
                            "point_name": item.point_name,
                            "point_count": item.point_count,
                            "cycle_type": item.cycle_type,
                            "cycle_count": item.cycle_count,
                            "frequency": item.frequency,
                            "sample_count": item.sample_count,
                            "sample_source": item.sample_source,
                            "is_subcontract": item.is_subcontract,
                            "remark": item.remark,
                        }
                    )
                    for item in items
                ],
                "attachments": [
                    {
                        "id": attachment.id,
                        "file_name": attachment.file_name,
                        "file_path": attachment.file_path,
                        "file_size": attachment.file_size,
                        "file_type": attachment.file_type,
                    }
                    for attachment in attachments
                ],
            }
            quotation_dict = CamelCaseUtil.transform_result(base_dict)
            quotation_list.append(quotation_dict)

        return quotation_list

    async def get_project_quotation(self, id: int):
        """
        获取项目报价详情

        :param id: 项目报价ID
        :return: 项目报价详情
        """
        # 查询项目报价
        stmt = select(ProjectQuotation).where(
            ProjectQuotation.id == id,
        )
        result = await self.db.execute(stmt)
        quotation = result.scalar_one_or_none()

        if not quotation:
            raise ServiceException(message=f"项目报价不存在：{id}")

        # 查询明细项
        items_stmt = select(ProjectQuotationItem).where(
            ProjectQuotationItem.project_quotation_id == quotation.id,
        )
        items_result = await self.db.execute(items_stmt)
        items = items_result.scalars().all()

        # 查询附件
        attachments_stmt = select(ProjectQuotationAttachment).where(
            ProjectQuotationAttachment.project_quotation_id == quotation.id,
        )
        attachments_result = await self.db.execute(attachments_stmt)
        attachments = attachments_result.scalars().all()

        # 查询其他费用
        other_fees_stmt = select(ProjectQuotationOtherFee).where(
            ProjectQuotationOtherFee.project_quotation_id == quotation.id
        )
        other_fees_result = await self.db.execute(other_fees_stmt)
        other_fees = other_fees_result.scalars().all()

        # 查询总费用
        total_fee_stmt = select(ProjectQuotationTotalFee).where(
            ProjectQuotationTotalFee.project_quotation_id == quotation.id
        )
        total_fee_result = await self.db.execute(total_fee_stmt)
        total_fee = total_fee_result.scalar_one_or_none()

        # 查询客服列表
        customer_support_list = await ProjectQuotationCustomerSupportService.get_project_quotation_customer_supports(
            self.db, quotation.id
        )
        customer_support_ids = await ProjectQuotationCustomerSupportService.get_customer_support_ids_by_quotation_id(
            self.db, quotation.id
        )

        # 构建基本信息字典
        base_dict = {
            "id": quotation.id,
            "project_name": quotation.project_name,
            "project_code": quotation.project_code,
            "business_type": quotation.business_type,
            "contract_code": quotation.contract_code,
            "customer_id": quotation.customer_id,
            "customer_name": quotation.customer_name,
            "customer_address": quotation.customer_address,
            "customer_contact": quotation.customer_contact,
            "customer_phone": quotation.customer_phone,
            "inspected_party": quotation.inspected_party,
            "inspected_contact": quotation.inspected_contact,
            "inspected_phone": quotation.inspected_phone,
            "inspected_address": quotation.inspected_address,
            "project_manager": quotation.project_manager,
            "market_manager": quotation.market_manager,
            "technical_manager": quotation.technical_manager,
            "commission_date": quotation.commission_date,
            # approvers字段已废弃，使用customer_support_list
            "customer_support_list": [
                CamelCaseUtil.transform_result(customer_support.__dict__) for customer_support in customer_support_list
            ],
            "customer_support_ids": customer_support_ids,
            "status": quotation.status,
            "remark": quotation.remark,
            "create_by": quotation.create_by,
            "create_time": quotation.create_time,
            "update_by": quotation.update_by,
            "update_time": quotation.update_time,
            "items": [
                CamelCaseUtil.transform_result(
                    {
                        "id": item.id,
                        "item_code": item.item_code,
                        "category": item.category,
                        "parameter": item.parameter,
                        "method": item.method,
                        "point_name": item.point_name,
                        "point_count": item.point_count,
                        "cycle_type": item.cycle_type,
                        "cycle_count": item.cycle_count,
                        "frequency": item.frequency,
                        "sample_count": item.sample_count,
                        "sample_source": item.sample_source,
                        "is_subcontract": item.is_subcontract,
                        "remark": item.remark,
                    }
                )
                for item in items
            ],
            "attachments": [
                CamelCaseUtil.transform_result(
                    {
                        "id": attachment.id,
                        "file_name": attachment.file_name,
                        "file_path": attachment.file_path,
                        "file_size": attachment.file_size,
                        "file_type": attachment.file_type,
                    }
                )
                for attachment in attachments
            ],
            "other_fees": [
                CamelCaseUtil.transform_result(
                    {
                        "id": fee.id,
                        "fee_name": fee.fee_name,
                        "quantity": fee.quantity,
                        "unit_price": fee.unit_price,
                        "total_price": fee.total_price,
                        "remark": fee.remark,
                    }
                )
                for fee in other_fees
            ],
            "total_fee": (
                CamelCaseUtil.transform_result(
                    {
                        "id": total_fee.id if total_fee else None,
                        "item_testing_fees": total_fee.item_testing_fees if total_fee else 100,
                        "discount_rate": total_fee.discount_rate if total_fee else 100,
                        "discounted_testing_fee": total_fee.discounted_testing_fee if total_fee else 0,
                        "other_fee": total_fee.other_fee if total_fee else 0,
                        "total_fee_before_discount": total_fee.total_fee_before_discount if total_fee else 0,
                        "tax_rate": total_fee.tax_rate if total_fee else 0,
                        "tax": total_fee.tax if total_fee else 0,
                        "total_fee_after_tax": total_fee.total_fee_after_tax if total_fee else 0,
                        "adjustment_amount": total_fee.adjustment_amount if total_fee else 0,
                        "final_amount": total_fee.final_amount if total_fee else 0,
                    }
                )
                if total_fee
                else None
            ),
        }

        # 使用CamelCaseUtil转换为驼峰命名
        return CamelCaseUtil.transform_result(base_dict)

    async def _can_user_submit_approval(self, project_quotation_id: int, current_user: CurrentUserModel) -> bool:
        """
        检查用户是否可以提交审批
        
        :param project_quotation_id: 项目报价ID
        :param current_user: 当前用户
        :return: 是否可以提交审批
        """
        # 管理员可以提交所有项目
        if 'admin' in current_user.roles:
            return True
        
        # 获取项目报价信息
        stmt = select(ProjectQuotation).where(ProjectQuotation.id == project_quotation_id)
        result = await self.db.execute(stmt)
        quotation = result.scalar_one_or_none()

        if not quotation:
            return False

        # 只有草稿状态的项目才能提交审批
        if quotation.status != "0":
            return False

        # 检查是否为创建人
        if quotation.create_by == current_user.user.user_id:
            return True

        # 检查是否为项目客服
        from module_quotation.dao.project_quotation_customer_support_dao import ProjectQuotationCustomerSupportDao
        customer_supports = await ProjectQuotationCustomerSupportDao.get_customer_supports_by_quotation_id(
            self.db, project_quotation_id
        )
        
        for support in customer_supports:
            if support.user_id == current_user.user.user_id:
                return True

        return False

    async def _can_user_withdraw_approval(self, project_quotation_id: int, current_user: CurrentUserModel) -> bool:
        """
        检查用户是否可以撤回审批
        
        :param project_quotation_id: 项目报价ID
        :param current_user: 当前用户
        :return: 是否可以撤回审批
        """
        # 管理员可以撤回所有项目
        if 'admin' in current_user.roles:
            return True
        
        # 获取项目报价信息
        stmt = select(ProjectQuotation).where(ProjectQuotation.id == project_quotation_id)
        result = await self.db.execute(stmt)
        quotation = result.scalar_one_or_none()

        if not quotation:
            return False

        # 只有待审核状态的项目才能撤回审批
        if quotation.status != "1":
            return False

        # 检查是否为创建人
        if quotation.create_by == current_user.user.user_id:
            return True

        # 检查是否为项目客服
        from module_quotation.dao.project_quotation_customer_support_dao import ProjectQuotationCustomerSupportDao
        customer_supports = await ProjectQuotationCustomerSupportDao.get_customer_supports_by_quotation_id(
            self.db, project_quotation_id
        )
        
        for support in customer_supports:
            if support.user_id == current_user.user.user_id:
                return True

        return False

    async def generate_project_code(self):
        """
        生成项目编号

        :return: 项目编号
        """
        # 生成项目编号：PJ_时间戳
        timestamp = int(time.time())
        return f"PJ_{timestamp}"

    async def generate_item_code(self):
        """
        生成项目明细编号

        :return: 项目明细编号
        """
        # 查询最大的项目明细编号
        stmt = select(func.max(ProjectQuotationItem.item_code)).where(
            ProjectQuotationItem.item_code.like("PN%"),
        )
        result = await self.db.execute(stmt)
        max_code = result.scalar()

        # 如果没有项目明细编号，则从PN00001开始
        if not max_code:
            return "PN00001"

        # 提取数字部分并加1
        try:
            num = int(max_code[2:]) + 1
            return f"PN{num:05d}"
        except (ValueError, IndexError):
            # 如果解析失败，则从PN00001开始
            return "PN00001"

    async def add_project_quotation(self, payload: AddProjectQuotationModel, current_user: CurrentUserModel):
        """
        添加项目报价

        :param payload: 添加项目报价模型
        :param current_user: 当前用户
        :return: 添加结果
        """
        try:
            # 生成项目编号
            project_code = await self.generate_project_code()

            # 由于新的数据结构不包含价格信息，暂时跳过价格计算
            # 后续可以根据技术手册和价格表来计算费用
            item_testing_fees = Decimal("0")

            # 创建项目报价对象
            project_quotation = ProjectQuotation(
                project_name=payload.project_name,
                project_code=project_code,
                business_type=payload.business_type,
                contract_code=payload.contract_code,
                customer_id=payload.customer_id,
                customer_name=payload.customer_name,
                customer_address=payload.customer_address,
                customer_contact=payload.customer_contact,
                customer_phone=payload.customer_phone,
                inspected_party=payload.inspected_party,
                inspected_contact=payload.inspected_contact,
                inspected_phone=payload.inspected_phone,
                inspected_address=payload.inspected_address,
                project_manager=payload.project_manager,
                market_manager=payload.market_manager,
                technical_manager=payload.technical_manager,
                commission_date=payload.commission_date,
                # approvers字段已废弃，审批人通过关联表管理
                status=payload.status,
                remark=payload.remark,
                create_by=current_user.user.user_name,
                create_time=datetime.now(),
                update_by=current_user.user.user_name,
                update_time=datetime.now(),
            )

            # 添加项目报价
            self.db.add(project_quotation)
            await self.db.flush()

            # 添加项目报价明细
            for item in payload.items:
                # 生成项目明细编号
                item_code = await self.generate_item_code()

                # 创建项目报价明细对象
                project_quotation_item = ProjectQuotationItem(
                    project_quotation_id=project_quotation.id,
                    item_code=item_code,
                    qualification_code=item.qualification_code,
                    classification=item.classification,
                    category=item.category,
                    parameter=item.parameter,
                    method=item.method,
                    limitation_scope=item.limitation_scope,
                    sample_source=item.sample_source,
                    point_name=item.point_name,
                    point_count=item.point_count or 1,
                    cycle_type=item.cycle_type,
                    cycle_count=item.cycle_count or 1,
                    frequency=item.frequency or 1,
                    sample_count=item.sample_count or 1,
                    is_subcontract=item.is_subcontract or "0",
                    special_consumables_price=item.special_consumables_price,
                    remark=item.remark,
                    create_by=current_user.user.user_name,
                    create_time=datetime.now(),
                    update_by=current_user.user.user_name,
                    update_time=datetime.now(),
                )

                # 添加项目报价明细
                self.db.add(project_quotation_item)

            # 添加项目报价附件
            for attachment in payload.attachments:
                # 创建项目报价附件对象
                project_quotation_attachment = ProjectQuotationAttachment(
                    project_quotation_id=project_quotation.id,
                    file_name=attachment.file_name,
                    file_path=attachment.file_path,
                    file_size=attachment.file_size,
                    file_type=attachment.file_type,
                    create_by=current_user.user.user_name,
                    create_time=datetime.now(),
                    update_by=current_user.user.user_name,
                    update_time=datetime.now(),
                )

                # 添加项目报价附件
                self.db.add(project_quotation_attachment)

            # 添加项目报价其他费用
            other_fee_prices = Decimal("0")
            for other_fee in payload.other_fees:
                # 创建项目报价其他费用对象
                other_fee_db = ProjectQuotationOtherFee(
                    project_quotation_id=project_quotation.id,
                    fee_name=other_fee.fee_name,
                    quantity=other_fee.quantity or 1,
                    unit_price=other_fee.unit_price or Decimal("0"),
                    remark=other_fee.remark,
                    create_by=current_user.user.user_name,
                    create_time=datetime.now(),
                    update_by=current_user.user.user_name,
                    update_time=datetime.now(),
                )
                other_fee_db.total_price = other_fee_db.quantity * other_fee_db.unit_price or Decimal("0")
                other_fee_prices += other_fee_db.total_price
                # 添加项目报价其他费用
                self.db.add(other_fee_db)

            # 处理客服
            if hasattr(payload, "customer_support_ids") and payload.customer_support_ids:
                from module_quotation.entity.vo.project_quotation_customer_support_vo import (
                    AddProjectQuotationCustomerSupportModel,
                )

                customer_support_model = AddProjectQuotationCustomerSupportModel(
                    project_quotation_id=project_quotation.id, user_ids=payload.customer_support_ids
                )
                await ProjectQuotationCustomerSupportService.add_project_quotation_customer_supports(
                    self.db, customer_support_model, current_user.user.user_name
                )

            # 提交事务
            await self.db.commit()

            # 同步基础价目表数据并计算费用
            fee_calculation_service = ProjectQuotationFeeCalculationService(self.db)
            await fee_calculation_service.sync_basedata_prices(project_quotation.id, current_user)

            # 计算项目费用
            fee_calculation = await fee_calculation_service.calculate_project_fees(project_quotation.id)

            # 添加项目报价总费用
            await self.update_project_quotation_total_fee(
                project_quotation_id=project_quotation.id,
                current_user=current_user,
                total_fee_model=None,
                item_testing_fees=fee_calculation.total_fee,
            )

            # 注意：不在新增时初始化审批记录，而是在提交审核时初始化

            return CrudResponseModel(is_success=True, message="添加成功", result=project_quotation.id)
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            raise ServiceException(message=f"添加项目报价失败：{str(e)}")

    async def edit_project_quotation(self, payload: EditProjectQuotationModel, current_user: CurrentUserModel):
        """
        编辑项目报价

        :param payload: 编辑项目报价模型
        :param current_user: 当前用户
        :return: 编辑结果
        """
        try:
            # 查询项目报价
            stmt = select(ProjectQuotation).where(
                ProjectQuotation.id == payload.id,
            )
            result = await self.db.execute(stmt)
            project_quotation = result.scalar_one_or_none()

            if not project_quotation:
                raise ServiceException(message=f"项目报价不存在：{payload.id}")

            # 检查报价单状态权限：只有草稿(0)或已拒绝(4)状态可以修改
            if project_quotation.status not in ["0", "4"]:
                status_map = {"1": "待审核", "2": "已审核", "3": "已撤回"}
                current_status = status_map.get(project_quotation.status, "未知状态")
                raise ServiceException(message=f"当前报价单状态为{current_status}，不允许修改")

            # 检查用户权限：只有创建人和项目中设置的客服人员可以修改
            await self._check_edit_permission(project_quotation, current_user)

            # 由于新的数据结构不包含价格信息，暂时跳过价格计算
            # 后续可以根据技术手册和价格表来计算费用
            item_testing_fees = Decimal("0")

            # 更新项目报价
            # project_quotation.project_name = payload.project_name
            project_quotation.business_type = getattr(payload, "business_type", project_quotation.business_type)
            project_quotation.contract_code = payload.contract_code
            project_quotation.customer_id = payload.customer_id
            project_quotation.customer_name = payload.customer_name
            project_quotation.customer_address = payload.customer_address
            project_quotation.customer_contact = payload.customer_contact
            project_quotation.customer_phone = payload.customer_phone
            project_quotation.inspected_party = payload.inspected_party
            project_quotation.inspected_contact = payload.inspected_contact
            project_quotation.inspected_phone = payload.inspected_phone
            project_quotation.inspected_address = payload.inspected_address
            project_quotation.project_manager = payload.project_manager
            project_quotation.market_manager = payload.market_manager
            project_quotation.technical_manager = payload.technical_manager
            project_quotation.commission_date = payload.commission_date
            # approvers字段已废弃，审批人通过关联表管理

            # 如果当前状态是已拒绝(4)，修改项目后自动变为草稿(0)
            if project_quotation.status == "4":
                project_quotation.status = "0"
            else:
                project_quotation.status = payload.status

            project_quotation.remark = payload.remark
            project_quotation.update_by = current_user.user.user_name
            project_quotation.update_time = datetime.now()

            # 物理删除原有的项目报价明细
            delete_items_stmt = delete(ProjectQuotationItem).where(
                ProjectQuotationItem.project_quotation_id == project_quotation.id
            )
            await self.db.execute(delete_items_stmt)

            # 添加新的项目报价明细
            for item in payload.items:

                # 生成项目明细编号
                item_code = item.item_code
                if not item_code:
                    item_code = await self.generate_item_code()

                # 创建项目报价明细对象
                project_quotation_item = ProjectQuotationItem(
                    project_quotation_id=project_quotation.id,
                    item_code=item_code,
                    qualification_code=item.qualification_code,
                    classification=item.classification,
                    category=item.category,
                    parameter=item.parameter,
                    method=item.method,
                    special_consumables_price=item.special_consumables_price,
                    limitation_scope=item.limitation_scope,
                    sample_source=item.sample_source,
                    point_name=item.point_name,
                    point_count=item.point_count or 1,
                    cycle_type=item.cycle_type,
                    cycle_count=item.cycle_count or 1,
                    frequency=item.frequency or 1,
                    sample_count=item.sample_count or 1,
                    is_subcontract=item.is_subcontract or "0",
                    remark=item.remark,
                    create_by=current_user.user.user_name,
                    create_time=datetime.now(),
                    update_by=current_user.user.user_name,
                    update_time=datetime.now(),
                )

                # 添加项目报价明细
                self.db.add(project_quotation_item)

            # 删除原有的项目报价附件
            delete_attachments_stmt = select(ProjectQuotationAttachment).where(
                ProjectQuotationAttachment.project_quotation_id == project_quotation.id,
            )
            delete_attachments_result = await self.db.execute(delete_attachments_stmt)
            delete_attachments = delete_attachments_result.scalars().all()

            for delete_attachment in delete_attachments:
                delete_attachment.update_by = current_user.user.user_name
                delete_attachment.update_time = datetime.now()

            # 添加新的项目报价附件
            for attachment in payload.attachments:
                # 如果有ID，则查询是否存在
                if attachment.id:
                    attachment_stmt = select(ProjectQuotationAttachment).where(
                        ProjectQuotationAttachment.id == attachment.id,
                        ProjectQuotationAttachment.project_quotation_id == project_quotation.id,
                    )
                    attachment_result = await self.db.execute(attachment_stmt)
                    existing_attachment = attachment_result.scalar_one_or_none()

                    if existing_attachment:
                        # 恢复已删除的项目报价附件
                        existing_attachment.file_name = attachment.file_name
                        existing_attachment.file_path = attachment.file_path
                        existing_attachment.file_size = attachment.file_size
                        existing_attachment.file_type = attachment.file_type
                        existing_attachment.update_by = current_user.user.user_name
                        existing_attachment.update_time = datetime.now()
                        continue

                # 创建项目报价附件对象
                project_quotation_attachment = ProjectQuotationAttachment(
                    project_quotation_id=project_quotation.id,
                    file_name=attachment.file_name,
                    file_path=attachment.file_path,
                    file_size=attachment.file_size,
                    file_type=attachment.file_type,
                    create_by=current_user.user.user_name,
                    create_time=datetime.now(),
                    update_by=current_user.user.user_name,
                    update_time=datetime.now(),
                )

                # 添加项目报价附件
                self.db.add(project_quotation_attachment)

            # 处理客服
            if hasattr(payload, "customer_support_ids") and payload.customer_support_ids:
                from module_quotation.entity.vo.project_quotation_customer_support_vo import (
                    AddProjectQuotationCustomerSupportModel,
                )

                customer_support_model = AddProjectQuotationCustomerSupportModel(
                    project_quotation_id=project_quotation.id, user_ids=payload.customer_support_ids
                )
                await ProjectQuotationCustomerSupportService.add_project_quotation_customer_supports(
                    self.db, customer_support_model, current_user.user.user_name
                )

            # 提交事务
            await self.db.commit()
            # 同步基础价目表数据并计算费用
            fee_calculation_service = ProjectQuotationFeeCalculationService(self.db)
            await fee_calculation_service.sync_basedata_prices(project_quotation.id, current_user)

            # 计算项目费用
            fee_calculation = await fee_calculation_service.calculate_project_fees(project_quotation.id)

            # 更新项目报价总费用
            await self.update_project_quotation_total_fee(
                project_quotation_id=project_quotation.id,
                current_user=current_user,
                total_fee_model=None,
                item_testing_fees=fee_calculation.total_fee,
            )

            return CrudResponseModel(is_success=True, message="编辑成功", result=project_quotation.id)
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            raise ServiceException(message=f"编辑项目报价失败：{str(e)}")

    async def update_project_quotation_fee(self, project_quotation: dict, current_user: CurrentUserModel):
        """
        更新项目报价费用

        :param project_quotation: 项目报价信息
        :param current_user: 当前用户
        :return: 更新结果
        """
        try:
            # 获取项目报价ID
            id = project_quotation.get("id")
            if not id:
                raise ServiceException(message="项目报价ID不能为空")

            # 查询项目报价
            stmt = select(ProjectQuotation).where(
                ProjectQuotation.id == id,
            )
            result = await self.db.execute(stmt)
            project_quotation_obj = result.scalar_one_or_none()

            if not project_quotation_obj:
                raise ServiceException(message=f"项目报价不存在：{id}")

            # 检查报价单状态权限：只有草稿(0)或已拒绝(4)状态可以修改
            if project_quotation_obj.status not in ["0", "4"]:
                status_map = {"1": "待审核", "2": "已审核", "3": "已撤回"}
                current_status = status_map.get(project_quotation_obj.status, "未知状态")
                raise ServiceException(message=f"当前报价单状态为{current_status}，不允许修改")

            # 检查用户权限：只有创建人和项目中设置的客服人员可以修改
            await self._check_edit_permission(project_quotation_obj, current_user)

            # 物理删除原有的项目报价其他费用
            delete_other_fees_stmt = delete(ProjectQuotationOtherFee).where(
                ProjectQuotationOtherFee.project_quotation_id == id
            )
            await self.db.execute(delete_other_fees_stmt)

            # 添加新的项目报价其他费用
            other_fees = project_quotation.get("otherFees", [])
            for other_fee in other_fees:
                # 创建项目报价其他费用对象
                project_quotation_other_fee = ProjectQuotationOtherFee(
                    project_quotation_id=id,
                    fee_name=other_fee.get("feeName"),
                    quantity=other_fee.get("quantity", 1),
                    unit_price=other_fee.get("unitPrice", 0),
                    total_price=other_fee.get("totalPrice", 0),
                    remark=other_fee.get("remark"),
                    create_by=current_user.user.user_name,
                    create_time=datetime.now(),
                    update_by=current_user.user.user_name,
                    update_time=datetime.now(),
                )

                # 添加项目报价其他费用
                self.db.add(project_quotation_other_fee)

            # 提交事务
            await self.db.commit()
            # 更新项目报价总费用
            total_fee_data = project_quotation.get("totalFee")
            if total_fee_data:
                # 创建总费用模型
                total_fee_model = ProjectQuotationTotalFeeModel.model_validate(
                    {
                        "discountRate": total_fee_data.get("discountRate", 100),
                        "taxRate": total_fee_data.get("taxRate", 0),
                        "adjustmentAmount": total_fee_data.get("adjustmentAmount", 0),
                    }
                )
                # 更新项目报价总费用
                await self.update_project_quotation_total_fee(
                    project_quotation_id=id, current_user=current_user, total_fee_model=total_fee_model
                )
            return CrudResponseModel(is_success=True, message="更新成功", result=id)
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            raise ServiceException(message=f"更新项目报价费用失败：{str(e)}")

    async def update_project_quotation_total_fee(
        self,
        project_quotation_id: int,
        current_user: CurrentUserModel,
        total_fee_model: Optional[ProjectQuotationTotalFeeModel] = None,
        item_testing_fees: Optional[Decimal] = None,
    ):
        """
        更新项目报价总费用(提交事务)

        :param project_quotation_id: 项目报价ID
        :param current_user: 当前用户
        :param total_fee_model: 总费用模型，如果为None则使用默认值
        :return: 更新后的总费用对象
        """
        try:
            # 验证项目报价是否存在
            project_stmt = select(ProjectQuotation).where(ProjectQuotation.id == project_quotation_id)
            project_result = await self.db.execute(project_stmt)
            project = project_result.scalar_one_or_none()

            if not project:
                raise ServiceException(message=f"项目报价不存在，ID: {project_quotation_id}")

            # 如果没有传入检测费用，则通过费用计算服务计算
            if item_testing_fees is None:
                fee_calculation_service = ProjectQuotationFeeCalculationService(self.db)
                fee_calculation = await fee_calculation_service.calculate_project_fees(project_quotation_id)
                item_testing_fees = fee_calculation.total_fee
            else:
                # 使用传入的检测费用
                item_testing_fees = item_testing_fees

            # 查询其他费用
            other_fees_stmt = select(ProjectQuotationOtherFee).where(
                ProjectQuotationOtherFee.project_quotation_id == project_quotation_id
            )
            other_fees_result = await self.db.execute(other_fees_stmt)
            other_fees = other_fees_result.scalars().all()

            # 计算其他费用总额
            other_fees_total = Decimal("0")
            for other_fee in other_fees:
                other_fee_total_price = other_fee.total_price
                other_fees_total += other_fee_total_price or Decimal("0")

            # 查询现有的项目报价总费用
            total_fee_stmt = select(ProjectQuotationTotalFee).where(
                ProjectQuotationTotalFee.project_quotation_id == project_quotation_id
            )
            total_fee_result = await self.db.execute(total_fee_stmt)
            existing_total_fee = total_fee_result.scalar_one_or_none()

            # 确定折扣率、税率和调整金额
            discount_rate = Decimal("100")
            tax_rate = Decimal("0")
            adjustment_amount = Decimal("0")

            # 如果存在现有记录，使用现有记录的值
            if existing_total_fee:
                discount_rate = existing_total_fee.discount_rate
                tax_rate = existing_total_fee.tax_rate
                adjustment_amount = existing_total_fee.adjustment_amount
            # 如果提供了新的总费用模型，则使用新的值覆盖
            if total_fee_model:
                discount_rate = (
                    total_fee_model.discount_rate if total_fee_model.discount_rate is not None else discount_rate
                )
                tax_rate = total_fee_model.tax_rate if total_fee_model.tax_rate is not None else tax_rate
                adjustment_amount = (
                    total_fee_model.adjustment_amount
                    if total_fee_model.adjustment_amount is not None
                    else adjustment_amount
                )
            # 计算折后检测费用
            discounted_testing_fee = item_testing_fees * discount_rate / 100
            # 计算优惠前总费用
            total_fee_before_discount = discounted_testing_fee + other_fees_total
            # 计算税费
            tax = total_fee_before_discount * tax_rate / 100
            # 计算优惠前总费用(税后)
            total_fee_after_tax = total_fee_before_discount + tax
            # 计算优惠后总金额
            final_amount = total_fee_after_tax + adjustment_amount
            # 存在则更新，不存在则插入
            if existing_total_fee:
                # 更新现有的总费用记录
                existing_total_fee.item_testing_fees = item_testing_fees
                existing_total_fee.discount_rate = discount_rate
                existing_total_fee.discounted_testing_fee = discounted_testing_fee
                existing_total_fee.other_fee = other_fees_total
                existing_total_fee.total_fee_before_discount = total_fee_before_discount
                existing_total_fee.tax_rate = tax_rate
                existing_total_fee.tax = tax
                existing_total_fee.total_fee_after_tax = total_fee_after_tax
                existing_total_fee.adjustment_amount = adjustment_amount
                existing_total_fee.final_amount = final_amount
                existing_total_fee.update_by = current_user.user.user_name
                existing_total_fee.update_time = datetime.now()

                # 刷新数据库
                await self.db.flush()
                # 提交事务
                await self.db.commit()
                # 返回更新后的记录
                project_quotation_total_fee = existing_total_fee
            else:
                # 创建新的总费用记录
                project_quotation_total_fee = ProjectQuotationTotalFee(
                    project_quotation_id=project_quotation_id,
                    item_testing_fees=item_testing_fees,
                    discount_rate=discount_rate,
                    discounted_testing_fee=discounted_testing_fee,
                    other_fee=other_fees_total,
                    total_fee_before_discount=total_fee_before_discount,
                    tax_rate=tax_rate,
                    tax=tax,
                    total_fee_after_tax=total_fee_after_tax,
                    adjustment_amount=adjustment_amount,
                    final_amount=final_amount,
                    create_by=current_user.user.user_name,
                    create_time=datetime.now(),
                    update_by=current_user.user.user_name,
                    update_time=datetime.now(),
                )

                # 添加到数据库
                self.db.add(project_quotation_total_fee)
                # 刷新数据库
                await self.db.flush()
                # 提交事务
                await self.db.commit()
            return project_quotation_total_fee
        except Exception as e:
            # 记录错误并重新抛出
            import traceback

            traceback.print_exc()
            raise ServiceException(message=f"更新项目报价总费用失败：{str(e)}")

    async def update_single_other_fee(self, data: dict, current_user: CurrentUserModel):
        """
        更新单行其他费用

        :param data: 其他费用信息
        :param current_user: 当前用户
        :return: 更新结果
        """
        try:
            # 获取项目报价ID
            id = data.get("id")
            if not id:
                raise ServiceException(message="项目报价ID不能为空")

            # 获取其他费用ID
            other_fee_id = data.get("otherFeeId")
            if not other_fee_id:
                raise ServiceException(message="其他费用ID不能为空")

            # 获取其他费用信息
            other_fee = data.get("otherFee")
            if not other_fee:
                raise ServiceException(message="其他费用信息不能为空")

            # 查询项目报价
            stmt = select(ProjectQuotation).where(
                ProjectQuotation.id == id,
            )
            result = await self.db.execute(stmt)
            project_quotation_obj = result.scalar_one_or_none()

            if not project_quotation_obj:
                raise ServiceException(message=f"项目报价不存在：{id}")

            # 检查报价单状态权限：只有草稿(0)或已拒绝(4)状态可以修改
            if project_quotation_obj.status not in ["0", "4"]:
                status_map = {"1": "待审核", "2": "已审核", "3": "已撤回"}
                current_status = status_map.get(project_quotation_obj.status, "未知状态")
                raise ServiceException(message=f"当前报价单状态为{current_status}，不允许修改")

            # 检查用户权限：只有创建人和项目中设置的客服人员可以修改
            await self._check_edit_permission(project_quotation_obj, current_user)

            # 查询其他费用
            other_fee_stmt = select(ProjectQuotationOtherFee).where(
                ProjectQuotationOtherFee.id == other_fee_id, ProjectQuotationOtherFee.project_quotation_id == id
            )
            other_fee_result = await self.db.execute(other_fee_stmt)
            other_fee_obj = other_fee_result.scalar_one_or_none()

            if not other_fee_obj:
                raise ServiceException(message=f"其他费用不存在：{other_fee_id}")

            # 更新其他费用
            other_fee_obj.fee_name = other_fee.get("feeName")
            other_fee_obj.quantity = other_fee.get("quantity", 1)
            other_fee_obj.unit_price = other_fee.get("unitPrice", 0)
            other_fee_obj.total_price = other_fee.get("totalPrice", 0)
            other_fee_obj.remark = other_fee.get("remark")
            other_fee_obj.update_by = current_user.user.user_name
            other_fee_obj.update_time = datetime.now()

            # 查询现有的项目报价总费用，获取折扣率、税率和调整金额
            total_fee_stmt = select(ProjectQuotationTotalFee).where(ProjectQuotationTotalFee.project_quotation_id == id)
            total_fee_result = await self.db.execute(total_fee_stmt)
            existing_total_fee = total_fee_result.scalar_one_or_none()

            # 创建总费用模型，保留原有的折扣率、税率和调整金额
            total_fee_model = None
            if existing_total_fee:
                total_fee_model = ProjectQuotationTotalFeeModel.model_validate(
                    {
                        "discountRate": existing_total_fee.discount_rate,
                        "taxRate": existing_total_fee.tax_rate,
                        "adjustmentAmount": existing_total_fee.adjustment_amount,
                    }
                )
            # 提交事务
            await self.db.commit()
            # 更新项目报价总费用
            await self.update_project_quotation_total_fee(id, current_user, total_fee_model)

            return CrudResponseModel(is_success=True, message="更新成功", result=other_fee_id)
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            raise ServiceException(message=f"更新其他费用失败：{str(e)}")

    async def delete_project_quotation(self, id: int, current_user: CurrentUserModel):
        """
        删除项目报价

        :param id: 项目报价ID
        :param current_user: 当前用户
        :return: 删除结果
        """
        try:
            # 查询项目报价
            stmt = select(ProjectQuotation).where(
                ProjectQuotation.id == id,
            )
            result = await self.db.execute(stmt)
            project_quotation = result.scalar_one_or_none()

            if not project_quotation:
                raise ServiceException(message=f"项目报价不存在：{id}")

            # 物理删除项目报价明细
            items_stmt = delete(ProjectQuotationItem).where(ProjectQuotationItem.project_quotation_id == id)
            await self.db.execute(items_stmt)

            # 物理删除项目报价附件
            attachments_stmt = delete(ProjectQuotationAttachment).where(
                ProjectQuotationAttachment.project_quotation_id == id
            )
            await self.db.execute(attachments_stmt)

            # 物理删除项目报价其他费用
            other_fees_stmt = delete(ProjectQuotationOtherFee).where(
                ProjectQuotationOtherFee.project_quotation_id == id
            )
            await self.db.execute(other_fees_stmt)

            # 物理删除项目报价总费用
            total_fee_stmt = delete(ProjectQuotationTotalFee).where(ProjectQuotationTotalFee.project_quotation_id == id)
            await self.db.execute(total_fee_stmt)

            # 物理删除项目报价主表数据
            delete_stmt = delete(ProjectQuotation).where(ProjectQuotation.id == id)
            await self.db.execute(delete_stmt)

            # 提交事务
            await self.db.commit()

            return CrudResponseModel(is_success=True, message="删除成功", result=None)
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            raise ServiceException(message=f"删除项目报价失败：{str(e)}")

    async def export_project_quotation(self, query_object: ProjectQuotationQueryModel, export_type: str):
        """
        导出项目报价

        :param query_object: 查询参数对象
        :param export_type: 导出类型
        :return: 导出文件响应
        """
        # 获取项目报价列表
        project_quotations = await self.get_project_quotation_list(query_object)

        # 转换为字典列表
        data = []
        for quotation in project_quotations:
            # 将SQLAlchemy模型转换为字典
            quotation_dict = {
                "id": quotation["id"],
                "project_name": quotation["project_name"],
                "project_code": quotation["project_code"],
                "contract_code": quotation["contract_code"] or "",
                "customer_name": quotation["customer_name"] or "",
                "inspected_party": quotation["inspected_party"] or "",
                "project_manager": quotation["project_manager"] or "",
                "market_manager": quotation["market_manager"] or "",
                "technical_manager": quotation["technical_manager"] or "",
                "commission_date": (
                    quotation["commission_date"].strftime("%Y-%m-%d") if quotation["commission_date"] else ""
                ),
                "status": self.get_status_label(quotation["status"]),
                "create_time": (
                    quotation["create_time"].strftime("%Y-%m-%d %H:%M:%S") if quotation["create_time"] else ""
                ),
                "remark": quotation["remark"] or "",
            }
            data.append(quotation_dict)

        # 定义表头映射
        headers = {
            "id": "ID",
            "project_name": "项目名称",
            "project_code": "项目编号",
            "contract_code": "合同编号",
            "customer_name": "客户名称",
            "inspected_party": "受检方企业名称",
            "project_manager": "项目负责人",
            "market_manager": "市场负责人",
            "technical_manager": "项目技术人",
            "commission_date": "委托日期",
            "status": "项目审批状态",
            # "total_amount": "总金额",
            "create_time": "创建时间",
            "remark": "备注",
        }

        # 导出文件
        return await ExportUtil.export_data(
            CamelCaseUtil.transform_result(data), CamelCaseUtil.transform_result(headers), "项目报价", export_type
        )

    async def export_project_quotation_detail(
        self, quotation_id: int, export_format: str = "excel"
    ) -> StreamingResponse:
        """
        导出项目报价单详情

        :param quotation_id: 项目报价ID
        :param export_format: 导出格式 (excel, pdf)
        :return: 导出文件响应
        """
        # 获取项目报价详情
        quotation_detail = await self.get_project_quotation(quotation_id)

        # 获取费用计算详情
        fee_calculation_service = ProjectQuotationFeeCalculationService(self.db)
        fee_calculation = await fee_calculation_service.calculate_project_fees(quotation_id)

        # 根据格式生成文件
        if export_format.lower() == "excel":
            return await self._generate_quotation_detail_excel(quotation_detail, fee_calculation)
        elif export_format.lower() == "pdf":
            return await self._generate_quotation_detail_pdf(quotation_detail, fee_calculation)
        else:
            raise ServiceException(message=f"不支持的导出格式: {export_format}")

    async def _generate_quotation_detail_excel(
        self, quotation_detail: dict, fee_calculation: ProjectQuotationFeeCalculationModel
    ) -> StreamingResponse:
        """
        生成项目报价单详情Excel文件（使用模板引擎）

        :param quotation_detail: 项目报价详情
        :param fee_calculation: 费用计算结果
        :return: Excel文件流响应
        """
        # 模板引擎
        template_engine = ExcelTemplateEngine()

        # 准备模板数据
        template_data = self._prepare_template_data(quotation_detail, fee_calculation)

        # 渲染模板
        output = template_engine.render_template(template_data)

        # 生成文件名
        project_name = quotation_detail.get("project_name", "项目报价单")
        safe_project_name = "".join(c for c in project_name if c.isalnum() or c in (" ", "-", "_")).rstrip()
        filename = f"{safe_project_name}_报价单_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        # 返回文件流
        return StreamingResponse(
            output,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f'attachment; filename="{filename.encode("ascii", errors="ignore").decode("ascii")}"; filename*=UTF-8\'\'{quote(filename)}'
            },
        )

    def _prepare_template_data(
        self, quotation_detail: dict, fee_calculation: ProjectQuotationFeeCalculationModel
    ) -> dict:
        """
        准备模板数据

        :param quotation_detail: 项目报价详情
        :param fee_calculation: 费用计算结果
        :return: 模板数据字典
        """
        # 从费用计算结果中获取明细数据
        items = []
        if hasattr(fee_calculation, "calculation_details") and fee_calculation.calculation_details:
            items = fee_calculation.calculation_details.details or []

        # 准备其他费用数据
        other_fees = quotation_detail.get("otherFees", [])
        total_fee = quotation_detail.get("totalFee", {})

        # 转换费用计算明细为模板需要的格式

        # 准备模板数据
        template_data = {
            # 基本信息 - 匹配模板中的变量名
            "projectName": quotation_detail.get("projectName", ""),
            "customerName": quotation_detail.get("customerName", ""),
            "inspectedParty": quotation_detail.get("inspectedParty", ""),
            "customerAddress": quotation_detail.get("customerAddress", ""),
            "customerContact": quotation_detail.get("customerContact", ""),
            "customerPhone": quotation_detail.get("customerPhone", ""),
            "inspectedContact": quotation_detail.get("inspectedContact", ""),
            "inspectedPhone": quotation_detail.get("inspectedPhone", ""),
            "inspectedAddress": quotation_detail.get("inspectedAddress", ""),
            "commissionDate": quotation_detail.get("commissionDate", ""),
            # 检测项目明细
            "items": items,
            # 其他费用
            "otherFees": other_fees,
            # 检测费用
            "totalSamplingFee": f"{float(fee_calculation.sampling_fee or 0):.2f}",
            "totalTestingFee": f"{float(fee_calculation.testing_fee or 0):.2f}",
            "totalPretreatmentFee": f"{float(fee_calculation.pretreatment_fee or 0):.2f}",
            "specialConsumablesFee": f"{float(fee_calculation.special_consumables_fee or 0):.2f}",
            # 汇总费用
            "totalFees": {
                "itemTestingFees": f"{float(total_fee.get('itemTestingFees', 0)):.2f}",
                "discountRate": f"{float(total_fee.get('discountRate', 0)):.1f}",
                "discountedTestingFee": f"{float(total_fee.get('discountedTestingFee', 0)):.2f}",
                "otherFee": f"{float(total_fee.get('otherFee', 0)):.2f}",
                "totalFeeBeforeDiscount": f"{float(total_fee.get('totalFeeBeforeDiscount', 0)):.2f}",
                "taxRate": f"{float(total_fee.get('taxRate', 0)):.1f}",
                "tax": f"{float(total_fee.get('tax', 0)):.2f}",
                "totalFeeAfterTax": f"{float(total_fee.get('totalFeeAfterTax', 0)):.2f}",
                "adjustmentAmount": f"{float(total_fee.get('adjustmentAmount', 0)):.2f}",
                "finalAmount": f"{float(total_fee.get('finalAmount', 0)):.2f}",
            },
        }

        return template_data

    async def _generate_quotation_detail_pdf(
        self, quotation_detail: dict, fee_calculation: ProjectQuotationFeeCalculationModel
    ) -> StreamingResponse:
        """
        生成项目报价单详情PDF文件

        :param quotation_detail: 项目报价详情
        :param fee_calculation: 费用计算结果
        :return: PDF文件流响应
        """
        # TODO: 实现PDF导出功能
        raise ServiceException(message="PDF导出功能暂未实现")

    async def _generate_quotation_detail_word(
        self, quotation_detail: dict, fee_calculation: ProjectQuotationFeeCalculationModel
    ) -> StreamingResponse:
        """
        生成项目报价单详情Word文件

        :param quotation_detail: 项目报价详情
        :param fee_calculation: 费用计算结果
        :return: Word文件流响应
        """
        # 创建Word文档
        doc = Document()

        # 设置页面边距
        sections = doc.sections
        for section in sections:
            section.top_margin = Inches(1)
            section.bottom_margin = Inches(1)
            section.left_margin = Inches(1)
            section.right_margin = Inches(1)

        # 1. 公司标题
        title = doc.add_heading("北京东西分析股份有限公司", level=1)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 2. 报价单标题
        subtitle = doc.add_heading("项目报价单", level=2)
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 3. 项目基本信息
        doc.add_heading("项目基本信息", level=3)

        # 创建基本信息表格
        info_table = doc.add_table(rows=5, cols=4)
        info_table.style = "Table Grid"
        info_table.alignment = WD_TABLE_ALIGNMENT.CENTER

        # 填充基本信息
        info_data = [
            [
                "项目名称",
                quotation_detail.get("project_name", ""),
                "项目编号",
                quotation_detail.get("project_code", ""),
            ],
            [
                "客户名称",
                quotation_detail.get("customer_name", ""),
                "受检方",
                quotation_detail.get("inspected_party", ""),
            ],
            [
                "项目负责人",
                quotation_detail.get("project_manager", ""),
                "市场负责人",
                quotation_detail.get("market_manager", ""),
            ],
            [
                "技术负责人",
                quotation_detail.get("technical_manager", ""),
                "委托日期",
                quotation_detail.get("commission_date", ""),
            ],
            [
                "合同编号",
                quotation_detail.get("contract_code", ""),
                "业务类别",
                quotation_detail.get("business_type", ""),
            ],
        ]

        for row_idx, row_data in enumerate(info_data):
            for col_idx, cell_data in enumerate(row_data):
                cell = info_table.cell(row_idx, col_idx)
                cell.text = str(cell_data)
                # 标签列加粗
                if col_idx in [0, 2]:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.bold = True

        # 4. 检测项目明细
        doc.add_heading("检测项目明细", level=3)

        items = quotation_detail.get("items", [])
        if items:
            # 创建明细表格
            detail_table = doc.add_table(rows=len(items) + 1, cols=17)
            detail_table.style = "Table Grid"

            # 表头
            headers = [
                "序号",
                "样品类别",
                "检测参数",
                "检测方法",
                "点位名称",
                "点位数",
                "周期",
                "频次",
                "样品数",
                "采样单价",
                "采样费",
                "检测首项价",
                "检测增项价",
                "检测费上限",
                "检测费",
                "前处理费",
                "备注",
            ]

            for col_idx, header in enumerate(headers):
                cell = detail_table.cell(0, col_idx)
                cell.text = header
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.bold = True

            # 填充明细数据
            total_sampling_fee = 0
            total_testing_fee = 0
            total_pretreatment_fee = 0

            for row_idx, item in enumerate(items, 1):
                sampling_fee = float(item.get("sampling_fee", 0))
                testing_fee = float(item.get("testing_fee", 0))
                pretreatment_fee = float(item.get("pretreatment_fee", 0))

                total_sampling_fee += sampling_fee
                total_testing_fee += testing_fee
                total_pretreatment_fee += pretreatment_fee

                row_data = [
                    str(row_idx),
                    item.get("category", ""),
                    item.get("parameter", ""),
                    item.get("method", ""),
                    item.get("point_name", ""),
                    str(item.get("point_count", "")),
                    str(item.get("cycle_count", "")),
                    str(item.get("frequency", "")),
                    str(item.get("sample_count", "")),
                    f"{float(item.get('sampling_price', 0)):.2f}",
                    f"{sampling_fee:.2f}",
                    f"{float(item.get('first_item_price', 0)):.2f}",
                    f"{float(item.get('additional_item_price', 0)):.2f}",
                    f"{float(item.get('testing_fee_limit', 0)):.2f}",
                    f"{testing_fee:.2f}",
                    f"{pretreatment_fee:.2f}",
                    item.get("remark", ""),
                ]

                for col_idx, cell_data in enumerate(row_data):
                    detail_table.cell(row_idx, col_idx).text = cell_data

        # 5. 费用汇总
        doc.add_heading("费用汇总", level=3)

        # 创建费用汇总表格
        fee_table = doc.add_table(rows=7, cols=4)
        fee_table.style = "Table Grid"

        fee_data = [
            ["采样费用", f"{total_sampling_fee:.2f}", "检测费用", f"{total_testing_fee:.2f}"],
            [
                "前处理费用",
                f"{total_pretreatment_fee:.2f}",
                "特殊耗材费",
                f"{float(fee_calculation.special_consumables_fee or 0):.2f}",
            ],
            [
                "其他费用",
                f"{float(fee_calculation.other_fees or 0):.2f}",
                "检测项目小计",
                f"{float(fee_calculation.subtotal_amount):.2f}",
            ],
            [
                "折扣率",
                f"{float(fee_calculation.discount_rate or 0):.1f}%",
                "折后金额",
                f"{float(fee_calculation.discounted_amount):.2f}",
            ],
            [
                "税率",
                f"{float(fee_calculation.tax_rate or 0):.1f}%",
                "税额",
                f"{float(fee_calculation.tax_amount or 0):.2f}",
            ],
            [
                "整体调整",
                f"{float(fee_calculation.adjustment_amount or 0):.2f}",
                "最终总价",
                f"{float(fee_calculation.final_amount):.2f}",
            ],
            ["", "", "", ""],  # 空行
        ]

        for row_idx, row_data in enumerate(fee_data):
            for col_idx, cell_data in enumerate(row_data):
                cell = fee_table.cell(row_idx, col_idx)
                cell.text = str(cell_data)
                # 标签列加粗
                if col_idx in [0, 2] and cell_data:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.bold = True

        # 6. 签字区域
        doc.add_heading("审批签字", level=3)

        # 创建签字表格
        signature_table = doc.add_table(rows=2, cols=3)
        signature_table.style = "Table Grid"

        # 签字标题
        signature_headers = ["制表人：", "审核人：", "批准人："]
        for col_idx, header in enumerate(signature_headers):
            signature_table.cell(0, col_idx).text = header

        # 日期行
        date_headers = ["日期：", "日期：", "日期："]
        for col_idx, header in enumerate(date_headers):
            signature_table.cell(1, col_idx).text = header

        # 保存到内存
        output = io.BytesIO()
        doc.save(output)
        output.seek(0)

        # 生成文件名
        project_name = quotation_detail.get("project_name", "项目报价单")
        safe_project_name = "".join(c for c in project_name if c.isalnum() or c in (" ", "-", "_")).rstrip()
        filename = f"{safe_project_name}_报价单_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"

        # 返回文件流
        return StreamingResponse(
            io.BytesIO(output.getvalue()),
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            headers={"Content-Disposition": f'attachment; filename="{filename}"'},
        )

    async def _generate_quotation_detail_pdf(
        self, quotation_detail: dict, fee_calculation: ProjectQuotationFeeCalculationModel
    ) -> StreamingResponse:
        """
        生成项目报价单详情PDF文件

        :param quotation_detail: 项目报价详情
        :param fee_calculation: 费用计算结果
        :return: PDF文件流响应
        """
        # 创建PDF文档
        output = io.BytesIO()
        doc = SimpleDocTemplate(output, pagesize=A4, topMargin=0.5 * inch, bottomMargin=0.5 * inch)

        # 获取样式
        styles = getSampleStyleSheet()

        # 创建自定义样式
        title_style = ParagraphStyle(
            "CustomTitle",
            parent=styles["Heading1"],
            fontSize=20,
            spaceAfter=30,
            alignment=1,  # 居中
            fontName="Helvetica-Bold",
        )

        subtitle_style = ParagraphStyle(
            "CustomSubtitle",
            parent=styles["Heading2"],
            fontSize=16,
            spaceAfter=20,
            alignment=1,  # 居中
            fontName="Helvetica-Bold",
        )

        heading_style = ParagraphStyle(
            "CustomHeading", parent=styles["Heading3"], fontSize=14, spaceAfter=12, fontName="Helvetica-Bold"
        )

        normal_style = ParagraphStyle("CustomNormal", parent=styles["Normal"], fontSize=10, fontName="Helvetica")

        # 构建PDF内容
        story = []

        # 1. 公司标题
        story.append(Paragraph("北京东西分析股份有限公司", title_style))
        story.append(Spacer(1, 12))

        # 2. 报价单标题
        story.append(Paragraph("项目报价单", subtitle_style))
        story.append(Spacer(1, 20))

        # 3. 项目基本信息
        story.append(Paragraph("项目基本信息", heading_style))

        # 基本信息表格
        info_data = [
            [
                "项目名称",
                quotation_detail.get("project_name", ""),
                "项目编号",
                quotation_detail.get("project_code", ""),
            ],
            [
                "客户名称",
                quotation_detail.get("customer_name", ""),
                "受检方",
                quotation_detail.get("inspected_party", ""),
            ],
            [
                "项目负责人",
                quotation_detail.get("project_manager", ""),
                "市场负责人",
                quotation_detail.get("market_manager", ""),
            ],
            [
                "技术负责人",
                quotation_detail.get("technical_manager", ""),
                "委托日期",
                quotation_detail.get("commission_date", ""),
            ],
            [
                "合同编号",
                quotation_detail.get("contract_code", ""),
                "业务类别",
                quotation_detail.get("business_type", ""),
            ],
        ]

        info_table = Table(info_data, colWidths=[1.5 * inch, 2 * inch, 1.5 * inch, 2 * inch])
        info_table.setStyle(
            TableStyle(
                [
                    ("BACKGROUND", (0, 0), (-1, -1), colors.white),
                    ("TEXTCOLOR", (0, 0), (-1, -1), colors.black),
                    ("ALIGN", (0, 0), (-1, -1), "LEFT"),
                    ("FONTNAME", (0, 0), (-1, -1), "Helvetica"),
                    ("FONTSIZE", (0, 0), (-1, -1), 10),
                    ("BOTTOMPADDING", (0, 0), (-1, -1), 12),
                    ("BACKGROUND", (0, 0), (0, -1), colors.lightgrey),
                    ("BACKGROUND", (2, 0), (2, -1), colors.lightgrey),
                    ("GRID", (0, 0), (-1, -1), 1, colors.black),
                ]
            )
        )
        story.append(info_table)
        story.append(Spacer(1, 20))

        # 4. 检测项目明细
        story.append(Paragraph("检测项目明细", heading_style))

        items = quotation_detail.get("items", [])
        if items:
            # 明细表头
            detail_headers = [
                "序号",
                "样品类别",
                "检测参数",
                "检测方法",
                "点位名称",
                "点位数",
                "周期",
                "频次",
                "样品数",
                "采样单价",
                "采样费",
                "检测首项价",
                "检测增项价",
                "检测费上限",
                "检测费",
                "前处理费",
                "备注",
            ]

            # 明细数据
            detail_data = [detail_headers]
            total_sampling_fee = 0
            total_testing_fee = 0
            total_pretreatment_fee = 0

            for idx, item in enumerate(items, 1):
                sampling_fee = float(item.get("sampling_fee", 0))
                testing_fee = float(item.get("testing_fee", 0))
                pretreatment_fee = float(item.get("pretreatment_fee", 0))

                total_sampling_fee += sampling_fee
                total_testing_fee += testing_fee
                total_pretreatment_fee += pretreatment_fee

                row_data = [
                    str(idx),
                    (
                        item.get("category", "")[:8] + "..."
                        if len(item.get("category", "")) > 8
                        else item.get("category", "")
                    ),
                    (
                        item.get("parameter", "")[:8] + "..."
                        if len(item.get("parameter", "")) > 8
                        else item.get("parameter", "")
                    ),
                    item.get("method", "")[:8] + "..." if len(item.get("method", "")) > 8 else item.get("method", ""),
                    (
                        item.get("point_name", "")[:6] + "..."
                        if len(item.get("point_name", "")) > 6
                        else item.get("point_name", "")
                    ),
                    str(item.get("point_count", "")),
                    str(item.get("cycle_count", "")),
                    str(item.get("frequency", "")),
                    str(item.get("sample_count", "")),
                    f"{float(item.get('sampling_price', 0)):.1f}",
                    f"{sampling_fee:.1f}",
                    f"{float(item.get('first_item_price', 0)):.1f}",
                    f"{float(item.get('additional_item_price', 0)):.1f}",
                    f"{float(item.get('testing_fee_limit', 0)):.1f}",
                    f"{testing_fee:.1f}",
                    f"{pretreatment_fee:.1f}",
                    item.get("remark", "")[:6] + "..." if len(item.get("remark", "")) > 6 else item.get("remark", ""),
                ]
                detail_data.append(row_data)

            # 创建明细表格（使用较小的列宽以适应页面）
            col_widths = [0.3 * inch] * 17  # 每列0.3英寸
            detail_table = Table(detail_data, colWidths=col_widths)
            detail_table.setStyle(
                TableStyle(
                    [
                        ("BACKGROUND", (0, 0), (-1, 0), colors.grey),
                        ("TEXTCOLOR", (0, 0), (-1, 0), colors.whitesmoke),
                        ("ALIGN", (0, 0), (-1, -1), "CENTER"),
                        ("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
                        ("FONTSIZE", (0, 0), (-1, 0), 8),
                        ("FONTNAME", (0, 1), (-1, -1), "Helvetica"),
                        ("FONTSIZE", (0, 1), (-1, -1), 7),
                        ("BOTTOMPADDING", (0, 0), (-1, -1), 6),
                        ("GRID", (0, 0), (-1, -1), 1, colors.black),
                    ]
                )
            )
            story.append(detail_table)

        story.append(Spacer(1, 20))

        # 5. 费用汇总
        story.append(Paragraph("费用汇总", heading_style))

        fee_data = [
            ["采样费用", f"{total_sampling_fee:.2f}", "检测费用", f"{total_testing_fee:.2f}"],
            [
                "前处理费用",
                f"{total_pretreatment_fee:.2f}",
                "特殊耗材费",
                f"{float(fee_calculation.special_consumables_fee or 0):.2f}",
            ],
            [
                "其他费用",
                f"{float(fee_calculation.other_fees or 0):.2f}",
                "检测项目小计",
                f"{float(fee_calculation.subtotal_amount):.2f}",
            ],
            [
                "折扣率",
                f"{float(fee_calculation.discount_rate or 0):.1f}%",
                "折后金额",
                f"{float(fee_calculation.discounted_amount):.2f}",
            ],
            [
                "税率",
                f"{float(fee_calculation.tax_rate or 0):.1f}%",
                "税额",
                f"{float(fee_calculation.tax_amount or 0):.2f}",
            ],
            [
                "整体调整",
                f"{float(fee_calculation.adjustment_amount or 0):.2f}",
                "最终总价",
                f"{float(fee_calculation.final_amount):.2f}",
            ],
        ]

        fee_table = Table(fee_data, colWidths=[1.5 * inch, 2 * inch, 1.5 * inch, 2 * inch])
        fee_table.setStyle(
            TableStyle(
                [
                    ("BACKGROUND", (0, 0), (-1, -1), colors.white),
                    ("TEXTCOLOR", (0, 0), (-1, -1), colors.black),
                    ("ALIGN", (0, 0), (-1, -1), "LEFT"),
                    ("FONTNAME", (0, 0), (-1, -1), "Helvetica"),
                    ("FONTSIZE", (0, 0), (-1, -1), 10),
                    ("BOTTOMPADDING", (0, 0), (-1, -1), 12),
                    ("BACKGROUND", (0, 0), (0, -1), colors.lightgrey),
                    ("BACKGROUND", (2, 0), (2, -1), colors.lightgrey),
                    ("GRID", (0, 0), (-1, -1), 1, colors.black),
                ]
            )
        )
        story.append(fee_table)
        story.append(Spacer(1, 30))

        # 6. 签字区域
        story.append(Paragraph("审批签字", heading_style))

        signature_data = [["制表人：", "审核人：", "批准人："], ["日期：", "日期：", "日期："]]

        signature_table = Table(signature_data, colWidths=[2.5 * inch, 2.5 * inch, 2.5 * inch])
        signature_table.setStyle(
            TableStyle(
                [
                    ("BACKGROUND", (0, 0), (-1, -1), colors.white),
                    ("TEXTCOLOR", (0, 0), (-1, -1), colors.black),
                    ("ALIGN", (0, 0), (-1, -1), "LEFT"),
                    ("FONTNAME", (0, 0), (-1, -1), "Helvetica"),
                    ("FONTSIZE", (0, 0), (-1, -1), 10),
                    ("BOTTOMPADDING", (0, 0), (-1, -1), 20),
                    ("GRID", (0, 0), (-1, -1), 1, colors.black),
                ]
            )
        )
        story.append(signature_table)

        # 构建PDF
        doc.build(story)
        output.seek(0)

        # 生成文件名
        project_name = quotation_detail.get("project_name", "项目报价单")
        safe_project_name = "".join(c for c in project_name if c.isalnum() or c in (" ", "-", "_")).rstrip()
        filename = f"{safe_project_name}_报价单_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        # 返回文件流
        return StreamingResponse(
            io.BytesIO(output.getvalue()),
            media_type="application/pdf",
            headers={"Content-Disposition": f'attachment; filename="{filename}"'},
        )

    def get_status_label(self, status: str):
        """
        获取状态标签

        :param status: 状态值
        :return: 状态标签
        """
        status_map = {"0": "草稿", "1": "待审核", "2": "审核完成", "3": "已撤回", "4": "已拒绝"}
        return status_map.get(status, "未知")

    def get_detailed_status_label(self, detailed_status: str):
        """
        获取详细状态标签

        :param detailed_status: 详细状态值
        :return: 详细状态标签
        """
        if detailed_status == "0":
            return "草稿"
        elif detailed_status == "2":
            return "审核完成"
        elif detailed_status == "3":
            return "已撤回"
        elif detailed_status == "4":
            return "已拒绝"
        elif detailed_status == "1-market":
            return "待审核（市场）"
        elif detailed_status == "1-order_confirm":
            return "待审核（项目成单确认）"
        elif detailed_status == "1-lab":
            return "待审核（实验室）"
        elif detailed_status == "1-field":
            return "待审核（现场）"
        elif detailed_status == "1-lab|field":
            return "待审核（实验室|现场）"
        elif detailed_status == "1":
            return "待审核"
        else:
            return "未知"

    async def get_project_quotation_detailed_status(self, id: int):
        """
        获取项目报价的详细状态

        :param id: 项目报价ID
        :return: 详细状态
        """
        # 查询项目报价
        stmt = select(ProjectQuotation).where(ProjectQuotation.id == id)
        result = await self.db.execute(stmt)
        quotation = result.scalar_one_or_none()

        if not quotation:
            return "0"  # 草稿

        # 如果不是待审核状态，直接返回原状态
        if quotation.status != "1":
            return quotation.status

        # 导入审批服务
        from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService

        approval_service = ProjectQuotationApprovalService(self.db)

        # 获取当前轮次的审批记录
        records_stmt = select(ProjectQuotationApprovalRecord).where(
            and_(
                ProjectQuotationApprovalRecord.project_quotation_id == id,
                ProjectQuotationApprovalRecord.is_current_round == "1",
            )
        )
        records_result = await self.db.execute(records_stmt)
        records = records_result.scalars().all()

        # 计算详细状态
        detailed_status = approval_service._calculate_detailed_status(records, quotation.business_type)

        # 如果项目状态是"1"但详细状态计算结果是"0"，说明审批记录有问题
        # 这种情况下应该返回"1"而不是"0"，避免前端按钮显示错误
        if detailed_status == "0" and quotation.status == "1":
            return "1"

        return detailed_status

    async def update_project_quotation_business_type(self, id: int, business_type: str, current_user: CurrentUserModel):
        """
        更新项目报价的业务类型

        :param id: 项目报价ID
        :param business_type: 业务类型
        :param current_user: 当前用户
        :return: 更新结果
        """
        # 查询项目报价
        stmt = select(ProjectQuotation).where(ProjectQuotation.id == id)
        result = await self.db.execute(stmt)
        quotation = result.scalar_one_or_none()

        if not quotation:
            raise ServiceException(message=f"项目报价不存在：{id}")

        # 更新业务类型和状态
        quotation.business_type = business_type
        quotation.status = "1"  # 待审核
        quotation.update_by = current_user.user.user_id
        quotation.update_time = datetime.now()

        # 提交更改
        await self.db.commit()
        await self.db.refresh(quotation)

        return True

    async def update_project_quotation_status(self, id: int, status: str, current_user: CurrentUserModel):
        """
        更新项目报价状态

        :param id: 项目报价ID
        :param status: 状态
        :param current_user: 当前用户
        :return: 更新结果
        """
        # 查询项目报价
        stmt = select(ProjectQuotation).where(ProjectQuotation.id == id)
        result = await self.db.execute(stmt)
        quotation = result.scalar_one_or_none()

        if not quotation:
            raise ServiceException(message=f"项目报价不存在：{id}")

        # 更新状态
        quotation.status = status
        quotation.update_by = current_user.user.user_id
        quotation.update_time = datetime.now()

        # 提交更改
        await self.db.commit()
        await self.db.refresh(quotation)

        return True

    async def _check_edit_permission(self, project_quotation: ProjectQuotation, current_user: CurrentUserModel):
        """
        检查用户是否有编辑项目报价的权限
        只有创建人和项目中设置的客服人员可以修改

        :param project_quotation: 项目报价对象
        :param current_user: 当前用户
        :raises ServiceException: 当用户没有权限时抛出异常
        """
        # 检查是否为管理员
        if current_user.user.admin:
            return

        # 检查是否为创建人
        if project_quotation.create_by == current_user.user.user_name:
            return

        # 检查是否为项目中设置的客服人员
        customer_support_stmt = select(ProjectQuotationCustomerSupport).where(
            and_(
                ProjectQuotationCustomerSupport.project_quotation_id == project_quotation.id,
                ProjectQuotationCustomerSupport.user_id == current_user.user.user_id,
            )
        )
        customer_support_result = await self.db.execute(customer_support_stmt)
        customer_support = customer_support_result.scalar_one_or_none()

        if customer_support:
            return

        # 如果都不满足，则抛出权限异常
        raise ServiceException(message="您没有权限修改此报价单，只有创建人和项目客服人员可以修改")

    async def get_approved_project_quotation_list(self, query_params: ProjectQuotationPageQueryModel):
        """
        获取已审批项目报价列表，用于采样任务分配页面

        :param query_params: 查询参数
        :return: 已审批项目报价列表
        """
        # 只查询已审批的项目报价（status = '2'）
        result = await self._get_project_quotation_page_base(query_params, is_approved_only=True)
        return CamelCaseUtil.transform_result(result)
