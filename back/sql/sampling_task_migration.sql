-- 采样任务模块数据库迁移脚本
-- 创建时间: 2024-01-01
-- 描述: 添加采样任务相关表结构

-- 1. 创建检测周期条目表
CREATE TABLE IF NOT EXISTS `detection_cycle_item` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `project_quotation_id` int(11) NOT NULL COMMENT '项目报价ID',
    `project_quotation_item_id` int(11) NOT NULL COMMENT '项目报价明细ID',
    `cycle_number` int(11) NOT NULL COMMENT '周期序号',
    `status` varchar(20) NOT NULL DEFAULT 'unassigned' COMMENT '状态：unassigned-未分配，assigned-已分配，completed-已完成',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_quotation_item_cycle` (`project_quotation_item_id`, `cycle_number`),
    KEY `idx_project_quotation_id` (`project_quotation_id`),
    KEY `idx_status` (`status`),
    CONSTRAINT `fk_detection_cycle_item_quotation` FOREIGN KEY (`project_quotation_id`) REFERENCES `project_quotation` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_detection_cycle_item_quotation_item` FOREIGN KEY (`project_quotation_item_id`) REFERENCES `project_quotation_item` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='检测周期条目表';

-- 2. 创建采样任务序列表
CREATE TABLE IF NOT EXISTS `sampling_task_sequence` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `year_month` varchar(6) NOT NULL COMMENT '年月(YYYYMM)',
    `sequence_number` int(11) NOT NULL DEFAULT 1 COMMENT '当月序号',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_year_month` (`year_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采样任务编号序列表';

-- 3. 创建采样任务表
CREATE TABLE IF NOT EXISTS `sampling_task` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `task_name` varchar(100) NOT NULL COMMENT '任务名称',
    `task_number` varchar(50) NOT NULL COMMENT '任务编号',
    `project_quotation_id` int(11) NOT NULL COMMENT '关联项目报价ID',
    `description` text COMMENT '任务描述',
    `assigned_user_id` int(11) DEFAULT NULL COMMENT '分配用户ID',
    `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态：pending-待执行，in_progress-执行中，completed-已完成',
    `planned_start_date` date DEFAULT NULL COMMENT '计划开始日期',
    `planned_end_date` date DEFAULT NULL COMMENT '计划结束日期',
    `actual_start_date` datetime DEFAULT NULL COMMENT '实际开始时间',
    `actual_end_date` datetime DEFAULT NULL COMMENT '实际结束时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_number` (`task_number`),
    KEY `idx_project_quotation_id` (`project_quotation_id`),
    KEY `idx_assigned_user_id` (`assigned_user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_planned_start_date` (`planned_start_date`),
    KEY `idx_planned_end_date` (`planned_end_date`),
    CONSTRAINT `fk_sampling_task_quotation` FOREIGN KEY (`project_quotation_id`) REFERENCES `project_quotation` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_sampling_task_user` FOREIGN KEY (`assigned_user_id`) REFERENCES `sys_user` (`user_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采样任务表';

-- 4. 创建采样任务与检测周期条目关联表
CREATE TABLE IF NOT EXISTS `sampling_task_cycle_item` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `sampling_task_id` int(11) NOT NULL COMMENT '采样任务ID',
    `detection_cycle_item_id` int(11) NOT NULL COMMENT '检测周期条目ID',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_cycle_item` (`sampling_task_id`, `detection_cycle_item_id`),
    KEY `idx_sampling_task_id` (`sampling_task_id`),
    KEY `idx_detection_cycle_item_id` (`detection_cycle_item_id`),
    CONSTRAINT `fk_task_cycle_item_task` FOREIGN KEY (`sampling_task_id`) REFERENCES `sampling_task` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_task_cycle_item_cycle` FOREIGN KEY (`detection_cycle_item_id`) REFERENCES `detection_cycle_item` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采样任务与检测周期条目关联表';

-- 5. 添加菜单权限
-- 采样管理主菜单
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('采样管理', 0, 6, 'sampling', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'sampling', 'admin', NOW(), '', NULL, '采样管理目录');

-- 获取采样管理菜单ID
SET @sampling_menu_id = LAST_INSERT_ID();

-- 检测周期条目管理
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('检测周期条目', @sampling_menu_id, 1, 'cycle-item', 'sampling/cycleItem/index', NULL, 1, 0, 'C', '0', '0', 'sampling:cycle-item:list', 'time', 'admin', NOW(), '', NULL, '检测周期条目菜单');

SET @cycle_item_menu_id = LAST_INSERT_ID();

-- 检测周期条目权限
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('检测周期条目查询', @cycle_item_menu_id, 1, '', '', NULL, 1, 0, 'F', '0', '0', 'sampling:cycle-item:query', '#', 'admin', NOW(), '', NULL, ''),
('检测周期条目生成', @cycle_item_menu_id, 2, '', '', NULL, 1, 0, 'F', '0', '0', 'sampling:cycle-item:generate', '#', 'admin', NOW(), '', NULL, ''),
('检测周期条目重新生成', @cycle_item_menu_id, 3, '', '', NULL, 1, 0, 'F', '0', '0', 'sampling:cycle-item:regenerate', '#', 'admin', NOW(), '', NULL, ''),
('检测周期条目修改', @cycle_item_menu_id, 4, '', '', NULL, 1, 0, 'F', '0', '0', 'sampling:cycle-item:edit', '#', 'admin', NOW(), '', NULL, ''),
('检测周期条目删除', @cycle_item_menu_id, 5, '', '', NULL, 1, 0, 'F', '0', '0', 'sampling:cycle-item:remove', '#', 'admin', NOW(), '', NULL, ''),
('检测周期条目导出', @cycle_item_menu_id, 6, '', '', NULL, 1, 0, 'F', '0', '0', 'sampling:cycle-item:export', '#', 'admin', NOW(), '', NULL, '');

-- 采样任务管理
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('采样任务', @sampling_menu_id, 2, 'task', 'sampling/task/index', NULL, 1, 0, 'C', '0', '0', 'sampling:task:list', 'list', 'admin', NOW(), '', NULL, '采样任务菜单');

SET @sampling_task_menu_id = LAST_INSERT_ID();

-- 采样任务权限
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('采样任务查询', @sampling_task_menu_id, 1, '', '', NULL, 1, 0, 'F', '0', '0', 'sampling:task:query', '#', 'admin', NOW(), '', NULL, ''),
('采样任务新增', @sampling_task_menu_id, 2, '', '', NULL, 1, 0, 'F', '0', '0', 'sampling:task:add', '#', 'admin', NOW(), '', NULL, ''),
('采样任务修改', @sampling_task_menu_id, 3, '', '', NULL, 1, 0, 'F', '0', '0', 'sampling:task:edit', '#', 'admin', NOW(), '', NULL, ''),
('采样任务删除', @sampling_task_menu_id, 4, '', '', NULL, 1, 0, 'F', '0', '0', 'sampling:task:remove', '#', 'admin', NOW(), '', NULL, ''),
('采样任务导出', @sampling_task_menu_id, 5, '', '', NULL, 1, 0, 'F', '0', '0', 'sampling:task:export', '#', 'admin', NOW(), '', NULL, ''),
('采样任务分配', @sampling_task_menu_id, 6, '', '', NULL, 1, 0, 'F', '0', '0', 'sampling:task:assign', '#', 'admin', NOW(), '', NULL, '');

-- 采样任务分配管理
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('采样任务分配', @sampling_menu_id, 3, 'task-assignment', 'sampling/task-assignment/index', NULL, 1, 0, 'C', '0', '0', 'sampling:assignment:list', 'assignment', 'admin', NOW(), '', NULL, '采样任务分配菜单');

SET @assignment_menu_id = LAST_INSERT_ID();

-- 采样任务分配权限
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('任务分配查询', @assignment_menu_id, 1, '', '', NULL, 1, 0, 'F', '0', '0', 'sampling:assignment:query', '#', 'admin', NOW(), '', NULL, ''),
('任务分配操作', @assignment_menu_id, 2, '', '', NULL, 1, 0, 'F', '0', '0', 'sampling:assignment:assign', '#', 'admin', NOW(), '', NULL, '');

-- 6. 创建触发器：项目报价审批通过后自动生成检测周期条目
DELIMITER //

CREATE TRIGGER `tr_project_quotation_approved` 
AFTER UPDATE ON `project_quotation`
FOR EACH ROW
BEGIN
    -- 当项目报价状态从非审核完成变为审核完成时，生成检测周期条目
    IF OLD.status != '2' AND NEW.status = '2' THEN
        -- 调用存储过程生成检测周期条目
        CALL sp_generate_detection_cycle_items(NEW.id, NEW.update_by);
    END IF;
END//

DELIMITER ;

-- 7. 创建存储过程：生成检测周期条目
DELIMITER //

CREATE PROCEDURE `sp_generate_detection_cycle_items`(
    IN p_project_quotation_id INT,
    IN p_create_by VARCHAR(50)
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_item_id INT;
    DECLARE v_cycle_count INT;
    DECLARE v_cycle_num INT;
    
    -- 声明游标
    DECLARE item_cursor CURSOR FOR 
        SELECT id, IFNULL(cycle_count, 1) as cycle_count
        FROM project_quotation_item 
        WHERE project_quotation_id = p_project_quotation_id;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 删除已存在的检测周期条目（如果有）
    DELETE FROM detection_cycle_item WHERE project_quotation_id = p_project_quotation_id;
    
    -- 打开游标
    OPEN item_cursor;
    
    -- 循环处理每个项目报价明细
    item_loop: LOOP
        FETCH item_cursor INTO v_item_id, v_cycle_count;
        IF done THEN
            LEAVE item_loop;
        END IF;
        
        -- 为每个明细生成对应数量的周期条目
        SET v_cycle_num = 1;
        WHILE v_cycle_num <= v_cycle_count DO
            INSERT INTO detection_cycle_item (
                project_quotation_id,
                project_quotation_item_id,
                cycle_number,
                status,
                create_by,
                create_time
            ) VALUES (
                p_project_quotation_id,
                v_item_id,
                v_cycle_num,
                'unassigned',
                p_create_by,
                NOW()
            );
            
            SET v_cycle_num = v_cycle_num + 1;
        END WHILE;
        
    END LOOP;
    
    -- 关闭游标
    CLOSE item_cursor;
    
END//

DELIMITER ;

-- 8. 为现有已审核的项目报价生成检测周期条目
-- 注意：这个操作会为所有状态为'2'（已审核）的项目报价生成检测周期条目
-- 如果不需要为历史数据生成，可以注释掉以下代码

/*
INSERT INTO detection_cycle_item (
    project_quotation_id,
    project_quotation_item_id,
    cycle_number,
    status,
    create_by,
    create_time
)
SELECT 
    pq.id as project_quotation_id,
    pqi.id as project_quotation_item_id,
    cycle_nums.cycle_number,
    'unassigned' as status,
    'system' as create_by,
    NOW() as create_time
FROM project_quotation pq
INNER JOIN project_quotation_item pqi ON pq.id = pqi.project_quotation_id
CROSS JOIN (
    SELECT 1 as cycle_number UNION ALL
    SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL
    SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
) cycle_nums
WHERE pq.status = '2'
  AND cycle_nums.cycle_number <= IFNULL(pqi.cycle_count, 1)
  AND NOT EXISTS (
      SELECT 1 FROM detection_cycle_item dci 
      WHERE dci.project_quotation_id = pq.id 
        AND dci.project_quotation_item_id = pqi.id 
        AND dci.cycle_number = cycle_nums.cycle_number
  );
*/

-- 9. 创建索引优化查询性能
CREATE INDEX `idx_detection_cycle_item_composite` ON `detection_cycle_item` (`project_quotation_id`, `status`, `cycle_number`);
CREATE INDEX `idx_sampling_task_composite` ON `sampling_task` (`project_quotation_id`, `status`, `assigned_user_id`);
CREATE INDEX `idx_sampling_task_cycle_item_composite` ON `sampling_task_cycle_item` (`sampling_task_id`, `detection_cycle_item_id`);

-- 迁移脚本执行完成
SELECT 'Sampling task migration completed successfully!' as message;