#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采样任务分配控制器
"""

from typing import List
from fastapi import APIRouter, Depends, Request, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_quotation.entity.vo.project_quotation_vo import ProjectQuotationPageQueryModel
from module_quotation.service.project_quotation_service import ProjectQuotationService
from module_sampling.entity.vo.sampling_task_assignment_vo import (
    SamplingTaskAssignmentModel,
    ProjectQuotationItemCycleModel
)
from module_sampling.service.sampling_task_assignment_service import SamplingTaskAssignmentService
from utils.response_util import ResponseUtil

router = APIRouter(
    prefix="/sampling/assignment",
    tags=["采样任务分配管理"],
)


@router.get("/approved-quotations/page", summary="分页查询已审批项目报价列表")
async def get_approved_quotations_page(
    request: Request,
    query_params: ProjectQuotationPageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    分页查询已审批项目报价列表，用于采样任务分配页面

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 已审批项目报价分页查询结果
    """
    try:
        service = ProjectQuotationService(db)
        result = await service.get_approved_project_quotation_list(query_params)
        return ResponseUtil.success(data=result)
    except Exception as e:
        return ResponseUtil.error(msg=f"查询失败: {str(e)}")


@router.get("/quotation/{quotation_id}/cycle-items", summary="获取项目报价的检测周期条目")
async def get_quotation_cycle_items(
    quotation_id: int,
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取项目报价的检测周期条目，按检测项目分组

    :param quotation_id: 项目报价ID
    :param request: 请求对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 检测周期条目列表
    """
    try:
        service = SamplingTaskAssignmentService(db)
        result = await service.get_quotation_cycle_items(quotation_id)
        return ResponseUtil.success(data=result)
    except Exception as e:
        return ResponseUtil.error(msg=f"查询失败: {str(e)}")


@router.post("/create-task", summary="创建采样任务")
async def create_sampling_task(
    assignment_data: SamplingTaskAssignmentModel,
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    创建采样任务

    :param assignment_data: 采样任务分配数据
    :param request: 请求对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 创建结果
    """
    from utils.log_util import logger
    
    try:
        logger.info(f"开始创建采样任务，用户ID: {current_user.user.user_id}")
        logger.info(f"请求数据: {assignment_data.dict()}")
        
        service = SamplingTaskAssignmentService(db)
        result = await service.create_sampling_task(assignment_data, current_user.user.user_id)
        
        logger.info(f"采样任务创建成功，任务ID: {result.task_id}")
        return ResponseUtil.success(data=result, msg="采样任务创建成功")
    except Exception as e:
        logger.error(f"创建采样任务失败: {str(e)}")
        logger.exception(e)
        return ResponseUtil.error(msg=f"创建失败: {str(e)}")